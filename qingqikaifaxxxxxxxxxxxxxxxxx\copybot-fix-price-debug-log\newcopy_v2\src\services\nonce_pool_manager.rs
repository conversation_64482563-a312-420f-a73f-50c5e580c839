use solana_sdk::{
    pubkey::Pubkey, 
    hash::Hash,
    signature::Keypair,
};
use solana_client::nonblocking::rpc_client::RpcClient;
use anyhow::{Result, Context};
use tracing::{info, warn};
use std::sync::Arc;

use crate::services::{
    nonce_pool::NonceAccountPool,
    nonce_file_manager::NonceFileManager,
};

/// Nonce账户池管理器 - 完全基于文件的实现
pub struct NoncePoolManager {
    file_manager: NonceFileManager,
    target_pool_size: usize,
}

impl NoncePoolManager {
    pub fn new(rpc_client: Arc<RpcClient>, payer: Arc<Keypair>) -> Self {
        Self {
            file_manager: NonceFileManager::new(rpc_client, payer),
            target_pool_size: 5, // 默认值
        }
    }

    /// 初始化nonce账户池 - 新的文件化流程
    /// 1. 检查本地文件是否存在
    /// 2. 如果不存在，根据配置创建账户
    /// 3. 从文件加载账户到内存池
    pub async fn initialize_pool(&self, target_pool_size: usize) -> Result<()> {
        info!("🚀 开始初始化基于文件的Nonce账户池，目标数量: {}", target_pool_size);

        // 设置池大小配置
        let pool = NonceAccountPool::get_instance();
        pool.set_pool_size(target_pool_size);

        let accounts = if self.file_manager.file_exists() {
            // 情况1: 文件存在，直接从文件加载
            info!("📂 发现现有nonce文件，从文件加载账户");
            let existing_accounts = self.file_manager.load_from_file()?;
            
            if existing_accounts.len() < target_pool_size {
                // 现有账户不足，需要创建更多
                let needed = target_pool_size - existing_accounts.len();
                info!("📊 现有 {} 个账户，需要补充 {} 个", existing_accounts.len(), needed);
                
                let new_accounts = self.file_manager.create_accounts_by_config(needed).await?;
                
                // 合并现有和新创建的账户
                let mut all_accounts = existing_accounts;
                all_accounts.extend(new_accounts);
                all_accounts
            } else {
                info!("✅ 现有 {} 个账户已满足需求", existing_accounts.len());
                existing_accounts
            }
        } else {
            // 情况2: 文件不存在，根据配置创建全部账户
            info!("📭 未发现nonce文件，根据配置创建 {} 个新账户", target_pool_size);
            self.file_manager.create_accounts_by_config(target_pool_size).await?
        };

        // 初始化内存账户池
        pool.init_pool(accounts);

        info!("✅ 基于文件的Nonce账户池初始化完成，总计 {} 个账户", pool.get_all_addresses().len());
        Ok(())
    }

    /// 检查并确保nonce文件存在，如果不存在则创建
    /// 这个方法在系统启动的早期阶段调用，在哈希预热之前
    pub async fn ensure_nonce_file_exists(&self, target_pool_size: usize) -> Result<bool> {
        if !self.file_manager.file_exists() {
            info!("📝 Nonce文件不存在，开始创建 {} 个账户", target_pool_size);
            let accounts = self.file_manager.create_accounts_by_config(target_pool_size).await?;
            
            if accounts.is_empty() {
                warn!("⚠️ 未能创建任何nonce账户");
                return Ok(false);
            }
            info!("✅ 成功创建并保存 {} 个nonce账户到文件", accounts.len());
            return Ok(true);
        }

        // 文件存在，检查账户数量是否足够
        let existing_accounts = self.file_manager.load_from_file()?;
        let current_count = existing_accounts.len();
        
        if current_count < target_pool_size {
            let needed = target_pool_size - current_count;
            info!("🔨 补充创建 {} 个nonce账户中...", needed);
            
            // 创建不足的账户
            let new_accounts = self.file_manager.create_accounts_by_config(needed).await?;
            
            // 合并现有和新创建的账户
            let mut all_accounts = existing_accounts;
            all_accounts.extend(new_accounts);
            
            // 保存合并后的账户列表
            self.file_manager.save_to_file(&all_accounts)?;
            info!("✅ 成功创建 {} 个账户，总数: {}", needed, all_accounts.len());
        } else {
            info!("✅ Nonce文件已存在，账户数量充足 ({}/{})", current_count, target_pool_size);
        }

        Ok(true)
    }

    /// 从文件快速加载账户到内存池（用于系统重启时）
    pub fn load_from_file_to_pool(&self) -> Result<()> {        
        let accounts = self.file_manager.load_from_file()
            .context("从文件加载nonce账户失败")?;

        if accounts.is_empty() {
            return Err(anyhow::anyhow!("文件中没有可用的nonce账户"));
        }

        // 直接初始化内存池
        let pool = NonceAccountPool::get_instance();
        pool.init_pool(accounts);

        Ok(())
    }

    /// 同步内存池状态到文件（可选，用于定期备份）
    pub async fn sync_pool_to_file(&self) -> Result<()> {
        let pool = NonceAccountPool::get_instance();
        let addresses = pool.get_all_addresses();
        
        if addresses.is_empty() {
            info!("🔕 内存池为空，跳过同步");
            return Ok(());
        }

        info!("🔄 同步内存池到文件，账户数量: {}", addresses.len());
        
        // 构建账户数据
        let mut accounts = Vec::new();
        for address in addresses {
            if let Some(nonce_value) = pool.get_nonce_for_address(&address) {
                accounts.push((address, nonce_value));
            }
        }

        // 保存到文件
        self.file_manager.save_to_file(&accounts)?;
        info!("✅ 内存池状态已同步到文件");
        
        Ok(())
    }

    /// 获取账户池状态
    pub fn get_pool_status(&self) -> String {
        let pool = NonceAccountPool::get_instance();
        let file_exists = self.file_manager.file_exists();
        
        format!(
            "内存池状态: {}, 文件存在: {}", 
            pool.get_pool_status(),
            file_exists
        )
    }

    /// 检查账户池是否已初始化
    pub fn is_pool_initialized(&self) -> bool {
        let pool = NonceAccountPool::get_instance();
        pool.is_initialized()
    }

    /// 检查是否需要扩展账户池
    pub fn check_pool_expansion_needed(&self) -> usize {
        let pool = NonceAccountPool::get_instance();
        pool.needs_more_accounts()
    }

    /// 获取文件管理器引用（用于高级操作）
    pub fn get_file_manager(&self) -> &NonceFileManager {
        &self.file_manager
    }
}
